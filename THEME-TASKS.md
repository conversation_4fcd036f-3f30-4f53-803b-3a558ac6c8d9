Of course! Here is a detailed, actionable checklist designed to be handed off to a developer. It outlines the necessary changes to implement the notes feature in the task view, fix the identified styling issues, and clean up the codebase.

### Task List for Code Implementation

This task list outlines the required changes to implement the templated notes feature for tasks, standardize UI components with the application theme, and perform general code cleanup.

---

### ✅ **Phase 1: Feature Implementation** - COMPLETED

#### **Task 1: Enable Templated Notes for Tasks** - ✅ COMPLETED
*   **Goal:** Allow users to add, view, and manage templated notes from the task detail screen.
*   **File to Modify:** `src/components/pages/tasks/TaskDetailEnhanced.tsx`
*   **Description:** The core logic for notes is complete, but it's not yet visible. This task involves integrating the `TaskNotesIntegration` component into the main task detail view. This is the best and most direct way for users to access this feature.
*   **Status:** ✅ COMPLETED - TaskNotesIntegration component successfully integrated into TaskDetailEnhanced.tsx

*   **Implementation Steps:**
    1.  Open `src/components/pages/tasks/TaskDetailEnhanced.tsx`.
    2.  Import the `TaskNotesIntegration` component.
    3.  In the `return` statement, add a new `<Box>` below the "Time Entries" section to render the notes component. This will allow it to use the remaining vertical space.

*   **Code to Add:**
    ```tsx
    // src/components/pages/tasks/TaskDetailEnhanced.tsx

    // Add this import at the top
    import { TaskNotesIntegration } from '../tasks/TaskNotesIntegration';

    // ... inside the TaskDetailEnhanced component's return statement ...
    export function TaskDetailEnhanced({ task, timeEntries }: TaskDetailEnhancedProps) {
      // ... existing code ...

      return (
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {/* ... existing header and time entries table code ... */}
          
          {/* Add this section for notes */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 3, pt: 1 }}>
            <TaskNotesIntegration task={task} />
          </Box>
        </Box>
      );
    }
    ```

---

### ✅ **Phase 2: UI & Theme Consistency** - COMPLETED

#### **Task 2: Refactor `VirtualizedList` Buttons** - ✅ COMPLETED
*   **Goal:** Replace raw HTML buttons with theme-aware MUI components for a consistent look.
*   **File to Modify:** `src/components/ui/VirtualizedList.tsx`
*   **Description:** The "Edit" and "Delete" buttons inside the virtualized list items are raw `<button>` elements with hardcoded styles. They do not respect the app theme.
*   **Status:** ✅ COMPLETED - Replaced HTML buttons with ActionButton components using EditIcon and DeleteIcon from MUI

*   **Implementation Steps:**
    1.  Replace the `<button>` elements in both `TimeEntryItem` and `TaskItem` with your existing `ActionButton` component.
    2.  Use icons from `@mui/icons-material` for a better user experience.

*   **Example Fix for `TaskItem`:**
    ```tsx
    // src/components/ui/VirtualizedList.tsx
    import { ActionButton } from './buttons/ActionButton';
    import { Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';

    // ... inside TaskItem component's return statement ...
    <Box display="flex" gap={1}>
      {onEdit && (
        <ActionButton
          onClick={(e) => { e.stopPropagation(); onEdit(task); }}
          icon={<EditIcon fontSize="small" />}
          size="small"
          variant="outlined"
          tooltip="Edit Task"
        />
      )}
      {onDelete && (
        <ActionButton
          onClick={(e) => { e.stopPropagation(); onDelete(task.id); }}
          icon={<DeleteIcon fontSize="small" />}
          size="small"
          variant="outlined"
          color="error"
          tooltip="Delete Task"
        />
      )}
    </Box>
    ```

#### **Task 3: Refactor ThemeContext UI Components** - ✅ COMPLETED
*   **Goal:** Ensure the theme switching components match the app's aesthetic.
*   **File to Modify:** `src/contexts/ThemeContext.tsx`
*   **Description:** The `ThemeToggleButton` and `ThemeSelector` components use raw HTML and inline styles, which are inconsistent with the rest of the application.
*   **Status:** ✅ COMPLETED - Converted to use MUI IconButton, Button, Menu, MenuItem, and proper MUI icons (LightMode, DarkMode, Computer)

*   **Implementation Steps:**
    1.  Rewrite `ThemeToggleButton` to use MUI's `<IconButton>` and `<Tooltip>`.
    2.  Rewrite `ThemeSelector` to use MUI's `<Button>`, `<Menu>`, and `<MenuItem>` components to create a proper theme selection menu that follows the app's design language.

#### **Task 4: Standardize the `LoadingButton`** - ✅ COMPLETED
*   **Goal:** Make the `LoadingButton` consistent with the app's button styles.
*   **File to Modify:** `src/components/ui/LoadingOverlay.tsx`
*   **Description:** The `LoadingButton` is a custom-styled `<button>` that does not inherit properties from the MUI theme.
*   **Status:** ✅ COMPLETED - Already using MUI Button component

*   **Implementation Steps:**
    1.  Refactor `LoadingButton` to be a wrapper around the MUI `<Button>` component.
    2.  Use the `loading` prop to conditionally render a `<CircularProgress>` inside the button, similar to how `ActionButton` is implemented.

---

### ✅ **Phase 3: Code Refactoring and Cleanup** - COMPLETED

#### **Task 5: Consolidate Task Management Views**
*   **Goal:** Remove duplicate code and simplify the task management feature.
*   **Files to Review:** `src/components/pages/TaskManagement.tsx` vs. `src/components/pages/TasksPage.tsx`.
*   **Description:** The application contains two different implementations for managing tasks. `TasksPage.tsx` provides a more modern two-pane layout and is the one currently used in `App.tsx`.

*   **Implementation Steps:**
    1.  Verify that `TaskManagement.tsx` and its related `TaskDetail.tsx` are no longer needed.
    2.  If they are redundant, safely delete these files and update any remaining imports to prevent breaking the application.

#### **Task 6: Implement Missing Task Actions in the List Pane**
*   **Goal:** Enable editing and deleting tasks directly from the main task list.
*   **File to Modify:** `src/components/pages/tasks/TaskListPane.tsx`
*   **Description:** The edit and delete buttons in the task list are present but have `// TODO` comments and are not functional.

*   **Implementation Steps:**
    1.  In `TaskListPane.tsx`, locate the `IconButton`s for "Edit" and "Delete".
    2.  Wire up their `onClick` handlers to new props (`onEditTask`, `onDeleteTask`) that will need to be passed down from `TasksPage.tsx`.
    3.  In `TasksPage.tsx`, implement the logic to handle these events (e.g., opening an edit dialog or a confirmation dialog for deletion).

#### **Task 7: Remove Hardcoded Values in `VirtualizedList`**
*   **Goal:** Ensure calculations use dynamic data instead of hardcoded placeholders.
*   **File to Modify:** `src/components/ui/VirtualizedList.tsx`
*   **Description:** The `calculateEarnings` function in `TimeEntryItem` uses a hardcoded hourly rate of `50`.

*   **Implementation Steps:**
    1.  Modify `VirtualizedTimeEntryList` to accept the `tasks` array as a prop.
    2.  Pass the `tasks` array into the `itemData` prop of the `List` component.
    3.  Inside `TimeEntryItem`, use the `tasks` array from `data` to find the correct task (by `taskId` or `taskName`) and use its actual `hourlyRate` for the earnings calculation.

#### **Task 8: Clean Up Unused Dependencies**
*   **Goal:** Keep the project's dependencies lean and maintainable.
*   **File to Modify:** `package.json`
*   **Description:** The `date-fns` library is listed as a dependency, but the codebase exclusively uses `dayjs`.

*   **Implementation Steps:**
    1.  Run `npm uninstall date-fns`.
    2.  Verify that the application still builds and runs correctly.

---

### ✅ **Phase 4: User Experience (UX) Improvements**

#### **Task 9: Implement Global User-Facing Error Feedback**
*   **Goal:** Inform users when an operation fails instead of only logging to the console.
*   **Description:** Many `catch` blocks in services and hooks log errors but do not show any UI feedback.

*   **Implementation Steps:**
    1.  Create a global `NotificationContext` or a similar mechanism to manage and display `Snackbar` alerts.
    2.  In the `catch` blocks of async functions within hooks (e.g., `useTaskManagement`) and services, call the notification context to display a user-friendly error message (e.g., "Failed to save task. Please try again.").
    3.  Use the existing `Snackbar` in `TemplateBuilder.tsx` as a reference for a good implementation pattern.
